@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 py-2 sm:py-6">
    <div class="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8">
        <!-- Mobile-friendly header -->
        <div class="flex items-center justify-between mb-4 sm:mb-6">
            <div>
                <h1 class="text-lg sm:text-2xl font-bold text-gray-900">KYC Details</h1>
                <p class="text-xs sm:text-sm text-gray-600 mt-1">{{ $user->name }}</p>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('agent.kyc.create', $user) }}" class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                    <span class="hidden sm:inline">Edit KYC</span>
                    <span class="sm:hidden">Edit</span>
                </a>
                <a href="{{ route('agent.users.show', $user) }}" class="inline-flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                    <span class="hidden sm:inline">Back to User</span>
                    <span class="sm:hidden">Back</span>
                </a>
            </div>
        </div>

        <!-- Status Alert -->
        @if($kyc->status === 'approved')
            <div class="bg-green-50 border border-green-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6" role="alert">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-semibold text-green-800">KYC Approved</p>
                        <p class="text-xs sm:text-sm text-green-700 mt-1">Verified on {{ $kyc->verified_at ? $kyc->verified_at->format('M d, Y \a\t g:i A') : 'N/A' }}</p>
                    </div>
                </div>
            </div>
        @elseif($kyc->status === 'pending')
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6" role="alert">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-semibold text-yellow-800">KYC Pending</p>
                        <p class="text-xs sm:text-sm text-yellow-700 mt-1">Submitted on {{ $kyc->created_at->format('M d, Y \a\t g:i A') }} - Awaiting verification</p>
                    </div>
                </div>
            </div>
        @elseif($kyc->status === 'rejected')
            <div class="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6" role="alert">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-semibold text-red-800">KYC Rejected</p>
                        <p class="text-xs sm:text-sm text-red-700 mt-1"><strong>Reason:</strong> {{ $kyc->rejection_reason }}</p>
                        <p class="text-xs sm:text-sm text-red-700">Please update the information and resubmit.</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Main Content Container -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-4 sm:p-6">
                
                <!-- Section: Identity Information -->
                <div class="border-b border-gray-200 pb-4 sm:pb-6 mb-4 sm:mb-6">
                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                        <svg class="h-5 w-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-4 0v2m4-2v2"></path>
                        </svg>
                        Identity Information
                    </h3>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ID Type</label>
                            <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                {{ ucwords(str_replace('_', ' ', $kyc->id_type)) }}
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ID Number</label>
                            <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->id_number }}</p>
                        </div>
                    </div>
                </div>

                <!-- Section: Document Images -->
                <div class="border-b border-gray-200 pb-4 sm:pb-6 mb-4 sm:mb-6">
                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                        <svg class="h-5 w-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        Uploaded Documents
                    </h3>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                        @foreach(['id_front_image' => 'ID Front', 'id_back_image' => 'ID Back', 'selfie_image' => 'Selfie Photo', 'address_proof_image' => 'Address Proof'] as $field => $label)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $label }}</label>
                                @if($kyc->$field)
                                    <div class="border border-gray-200 rounded-lg p-3 bg-gray-50">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                                <span class="text-sm text-gray-700">Document uploaded</span>
                                            </div>
                                            <a href="{{ $kyc->getDocumentUrl($field) }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                View
                                            </a>
                                        </div>
                                    </div>
                                @else
                                    <div class="border border-gray-200 rounded-lg p-3 bg-gray-50">
                                        <div class="flex items-center">
                                            <svg class="h-5 w-5 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                            </svg>
                                            <span class="text-sm text-gray-500">No document uploaded</span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Section: Address Information -->
                <div class="border-b border-gray-200 pb-4 sm:pb-6 mb-4 sm:mb-6">
                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                        <svg class="h-5 w-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Address Information
                    </h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Street Address</label>
                            <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->address }}</p>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->country }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">State/Region</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->state }}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Local Government Area</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->lga ?: 'Not specified' }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->city }}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->postal_code }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Community</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->community ?: 'Not specified' }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section: Personal Information -->
                <div class="pb-4 sm:pb-6">
                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                        <svg class="h-5 w-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Personal Information
                    </h3>

                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                            <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                {{ $kyc->date_of_birth ? $kyc->date_of_birth->format('M d, Y') : 'Not specified' }}
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                            <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ ucfirst($kyc->gender) }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Occupation</label>
                            <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $kyc->occupation }}</p>
                        </div>
                    </div>
                </div>

                <!-- Submission Information -->
                <div class="border-t border-gray-200 pt-4 sm:pt-6 mt-4 sm:mt-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">Submission Details</h4>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">Submitted on:</span>
                            <span class="text-gray-900 ml-2">{{ $kyc->created_at->format('M d, Y \a\t g:i A') }}</span>
                        </div>
                        @if($kyc->submittedByAgent)
                            <div>
                                <span class="text-gray-500">Submitted by:</span>
                                <span class="text-gray-900 ml-2">{{ $kyc->submittedByAgent->name }}</span>
                            </div>
                        @endif
                        @if($kyc->updated_at && $kyc->updated_at != $kyc->created_at)
                            <div>
                                <span class="text-gray-500">Last updated:</span>
                                <span class="text-gray-900 ml-2">{{ $kyc->updated_at->format('M d, Y \a\t g:i A') }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Bottom Spacing -->
        <div class="h-20 sm:h-0"></div>
    </div>
</div>
@endsection
