<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\EventRegistrationController;
use App\Http\Controllers\Admin\EventSpeakerController;
use App\Http\Controllers\Admin\EventBroadcastController;
use App\Http\Controllers\Admin\EventFormFieldController;
use App\Http\Controllers\Admin\EventBannerController;
use App\Http\Controllers\Admin\EventPartnerController;

/*
|--------------------------------------------------------------------------
| Admin Event Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin event routes for your application.
|
*/

// Test route without middleware for debugging
Route::get('/admin/test', function() {
    return 'Test route works!';
})->name('admin.test');

// Test route for event routes debugging
Route::get('/admin/events/test-routes', function() {
    return response()->json([
        'message' => 'Event routes are working',
        'timestamp' => now(),
        'routes_loaded' => true
    ]);
})->name('admin.events.test-routes');

// Documentation routes without middleware for debugging
Route::get('/admin/events/documentation', function() {
    return view('admin.events.documentation');
})->name('admin.events.documentation');

Route::get('/admin/events/mailchimp-docs', function() {
    return view('admin.events.mailchimp-docs');
})->name('admin.events.mailchimp-docs');

// Admin Event routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {

    // Events
    Route::resource('events', EventController::class);

    // Event Registrations
    Route::prefix('events/{event}/registrations')->name('events.registrations.')->group(function () {
        Route::get('/', [EventRegistrationController::class, 'index'])->name('index');
        Route::get('/create', [EventRegistrationController::class, 'create'])->name('create');
        Route::post('/', [EventRegistrationController::class, 'store'])->name('store');
        Route::get('/{registration}/edit', [EventRegistrationController::class, 'edit'])->name('edit');
        Route::put('/{registration}', [EventRegistrationController::class, 'update'])->name('update');
        Route::delete('/{registration}', [EventRegistrationController::class, 'destroy'])->name('destroy');
        Route::post('/{registration}/approve', [EventRegistrationController::class, 'approve'])->name('approve');
        Route::post('/{registration}/reject', [EventRegistrationController::class, 'reject'])->name('reject');
        Route::post('/{registration}/mark-attended', [EventRegistrationController::class, 'markAttended'])->name('mark-attended');
        Route::get('/export', [EventRegistrationController::class, 'export'])->name('export');
    });

    // Event Speakers
    Route::prefix('events/{event}/speakers')->name('events.speakers.')->group(function () {
        Route::get('/', [EventSpeakerController::class, 'index'])->name('index');
        Route::get('/create', [EventSpeakerController::class, 'create'])->name('create');
        Route::post('/', [EventSpeakerController::class, 'store'])->name('store');
        Route::get('/{speaker}/edit', [EventSpeakerController::class, 'edit'])->name('edit');
        Route::put('/{speaker}', [EventSpeakerController::class, 'update'])->name('update');
        Route::delete('/{speaker}', [EventSpeakerController::class, 'destroy'])->name('destroy');
        Route::post('/order', [EventSpeakerController::class, 'order'])->name('order');
    });

    // Event Banner Ads
    Route::prefix('events/{event}/banners')->name('events.banners.')->group(function () {
        Route::get('/', [EventBannerController::class, 'index'])->name('index');
        Route::post('/', [EventBannerController::class, 'store'])->name('store');
        Route::delete('/{banner}', [EventBannerController::class, 'destroy'])->name('destroy');
    });

    // Event Partner Logos
    Route::prefix('events/{event}/partners')->name('events.partners.')->group(function () {
        Route::get('/', [EventPartnerController::class, 'index'])->name('index');
        Route::post('/', [EventPartnerController::class, 'store'])->name('store');
        Route::delete('/{partner}', [EventPartnerController::class, 'destroy'])->name('destroy');
        Route::post('/order', [EventPartnerController::class, 'order'])->name('order');
    });

    // Event Broadcasts
    Route::prefix('events/{event}/broadcasts')->name('events.broadcasts.')->group(function () {
        Route::get('/', [EventBroadcastController::class, 'index'])->name('index');
        Route::get('/create', [EventBroadcastController::class, 'create'])->name('create');
        Route::post('/', [EventBroadcastController::class, 'store'])->name('store');
        Route::get('/{broadcast}', [EventBroadcastController::class, 'show'])->name('show');
        Route::post('/{broadcast}/send', [EventBroadcastController::class, 'send'])->name('send');
        Route::post('/test-email', [EventBroadcastController::class, 'testEmail'])->name('test-email');

        // Direct broadcast route (alternative method)
        Route::post('/{broadcast}/direct-send', [\App\Http\Controllers\Admin\DirectBroadcastController::class, 'send'])->name('direct-send');
    });

    // Event Form Fields
    Route::prefix('events/{event}/form-fields')->name('events.form-fields.')->group(function () {
        Route::get('/', [EventFormFieldController::class, 'index'])->name('index');
        Route::get('/create', [EventFormFieldController::class, 'create'])->name('create');
        Route::post('/', [EventFormFieldController::class, 'store'])->name('store');
        Route::get('/{formField}/edit', [EventFormFieldController::class, 'edit'])->name('edit');
        Route::put('/{formField}', [EventFormFieldController::class, 'update'])->name('update');
        Route::delete('/{formField}', [EventFormFieldController::class, 'destroy'])->name('destroy');
        Route::post('/order', [EventFormFieldController::class, 'updateOrder'])->name('order');
    });
});
