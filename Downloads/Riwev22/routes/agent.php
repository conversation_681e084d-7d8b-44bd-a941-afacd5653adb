<?php

use App\Http\Controllers\Agent\DashboardController;
use App\Http\Controllers\Agent\UserController;
use App\Http\Controllers\Agent\BankAccountController;
use App\Http\Controllers\Agent\FarmController;
use App\Http\Controllers\Agent\InsuranceController;
use App\Http\Controllers\Agent\ClaimController;
use App\Http\Controllers\Agent\WalletController;
use App\Http\Controllers\Agent\ActivityLogController;
use App\Http\Controllers\Agent\SettingsController;
use App\Http\Controllers\Agent\LeaderboardController;
use App\Http\Controllers\Agent\ServiceLocatorController;
use App\Http\Controllers\Agent\LoanRequestController;
use App\Http\Controllers\Agent\SupportTicketController;
use App\Http\Controllers\Agent\KYCController;
use App\Http\Controllers\Agent\LoanOfferController;
use App\Http\Controllers\Agent\UserWalletController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Agent Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the agent dashboard.
|
*/

Route::middleware(['auth', 'web', \App\Http\Middleware\AgentMiddleware::class])->prefix('agent')->name('agent.')->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.full');

    // User Management
    Route::resource('users', UserController::class);

    // Bank Account Management
    Route::prefix('users/{user}/bank-accounts')->name('users.bank-accounts.')->group(function () {
        Route::get('/', [BankAccountController::class, 'index'])->name('index');
        Route::get('/create', [BankAccountController::class, 'create'])->name('create');
        Route::post('/', [BankAccountController::class, 'store'])->name('store');
        Route::get('/{bankAccount}/edit', [BankAccountController::class, 'edit'])->name('edit');
        Route::put('/{bankAccount}', [BankAccountController::class, 'update'])->name('update');
        Route::delete('/{bankAccount}', [BankAccountController::class, 'destroy'])->name('destroy');
        Route::put('/{bankAccount}/set-default', [BankAccountController::class, 'setDefault'])->name('set-default');
    });

    // KYC Management
    Route::get('/users/{user}/kyc', [KYCController::class, 'show'])->name('kyc.show');
    Route::get('/users/{user}/kyc/create', [KYCController::class, 'create'])->name('kyc.create');
    Route::post('/users/{user}/kyc', [KYCController::class, 'store'])->name('kyc.store');

    // Rupa Management
    Route::get('/users/{user}/rupa', [UserController::class, 'showRupa'])->name('users.rupa');
    Route::get('/users/{user}/rupa/download/{format?}', [UserController::class, 'downloadRupaCard'])->name('users.rupa.download');

    // Farm Management
    Route::resource('farms', FarmController::class);
    Route::get('/farms/{farm}/weather', [FarmController::class, 'weather'])->name('farms.weather');
    Route::get('/farms/{farm}/insights', [FarmController::class, 'insights'])->name('farms.insights');
    Route::get('/farms/{farm}/risk', [FarmController::class, 'risk'])->name('farms.risk');

    // Insurance Policies
    Route::resource('insurance', InsuranceController::class);
    Route::get('/insurance/buy/{farm}', [InsuranceController::class, 'buyPolicy'])->name('insurance.buy');

    // Claims Management
    Route::resource('claims', ClaimController::class);
    Route::get('/claims/file/{policy}', [ClaimController::class, 'fileClaimForm'])->name('claims.file');
    Route::post('/claims/file/{policy}', [ClaimController::class, 'fileClaim'])->name('claims.submit');

    // Wallet
    Route::get('/wallet', [WalletController::class, 'index'])->name('wallet.index');
    Route::get('/wallet/deposit', [WalletController::class, 'showDepositForm'])->name('wallet.deposit');
    Route::post('/wallet/deposit', [WalletController::class, 'deposit'])->name('wallet.deposit.process');
    Route::get('/wallet/transactions', [WalletController::class, 'transactions'])->name('wallet.transactions');
    Route::get('/wallet/paystack/callback', [WalletController::class, 'paystackCallback'])->name('wallet.paystack.callback');

    // User Wallet Management
    Route::prefix('users/{user}/wallet')->name('users.wallet.')->group(function () {
        Route::get('/', [UserWalletController::class, 'index'])->name('index');
        Route::get('/fund', [UserWalletController::class, 'showFundForm'])->name('fund.form');
        Route::post('/fund', [UserWalletController::class, 'fund'])->name('fund');

        // Virtual Accounts
        Route::prefix('virtual-accounts')->name('virtual-accounts.')->group(function () {
            Route::get('/create', [UserWalletController::class, 'showCreateVirtualAccountForm'])->name('create');
            Route::post('/create', [UserWalletController::class, 'createVirtualAccount'])->name('store');
            Route::get('/{virtualAccount}', [UserWalletController::class, 'showVirtualAccount'])->name('show');
            Route::post('/{virtualAccount}/refresh', [UserWalletController::class, 'refreshVirtualAccount'])->name('refresh');
        });
    });

    // Loan Offers
    Route::prefix('users/{user}/loans/offers')->name('loans.offers.')->group(function () {
        Route::get('/', [LoanOfferController::class, 'index'])->name('index');
        Route::get('/{bankLoanOffer}/apply', [LoanOfferController::class, 'create'])->name('create');
        Route::post('/{bankLoanOffer}/apply', [LoanOfferController::class, 'store'])->name('store');
    });

    // Farm Activity Logs
    Route::get('/farms/{farm}/activities', [ActivityLogController::class, 'index'])->name('activities.index');
    Route::get('/farms/{farm}/activities/create', [ActivityLogController::class, 'create'])->name('activities.create');
    Route::post('/farms/{farm}/activities', [ActivityLogController::class, 'store'])->name('activities.store');
    Route::get('/farms/{farm}/activities/{activity}', [ActivityLogController::class, 'show'])->name('activities.show');
    Route::get('/farms/{farm}/activities/{activity}/edit', [ActivityLogController::class, 'edit'])->name('activities.edit');
    Route::put('/farms/{farm}/activities/{activity}', [ActivityLogController::class, 'update'])->name('activities.update');
    Route::delete('/farms/{farm}/activities/{activity}', [ActivityLogController::class, 'destroy'])->name('activities.destroy');

    // Settings
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [SettingsController::class, 'update'])->name('settings.update');
    Route::get('/settings/profile', [SettingsController::class, 'profile'])->name('settings.profile');
    Route::put('/settings/profile', [SettingsController::class, 'updateProfile'])->name('settings.profile.update');

    // Leaderboard
    Route::get('/leaderboard', [LeaderboardController::class, 'index'])->name('leaderboard');
    Route::post('/leaderboard/claim-reward', [LeaderboardController::class, 'claimReward'])->name('leaderboard.claim-reward');
    Route::post('/leaderboard/refresh-stats', [LeaderboardController::class, 'refreshStats'])->name('leaderboard.refresh-stats');

    // Service Locator
    Route::get('/service-locator', [ServiceLocatorController::class, 'index'])->name('service-locator.index');
    Route::get('/service-locator/farm/{farm}', [ServiceLocatorController::class, 'getFarmProviders'])->name('service-locator.farm-providers');
    Route::post('/service-locator/book/{provider}', [ServiceLocatorController::class, 'bookService'])->name('service-locator.book');

    // Activity Logs (Global routes)
    Route::get('/activities', [ActivityLogController::class, 'allActivities'])->name('activities.all');
    Route::get('/activities/create', [ActivityLogController::class, 'createGlobal'])->name('activities.create-global');
    Route::post('/activities', [ActivityLogController::class, 'storeGlobal'])->name('activities.store-global');

    // Loan Requests
    Route::get('/loans', [LoanRequestController::class, 'index'])->name('loans.index');
    Route::get('/loans/create/{farm?}', [LoanRequestController::class, 'create'])->name('loans.create');
    Route::post('/loans', [LoanRequestController::class, 'store'])->name('loans.store');
    Route::get('/loans/{loan}', [LoanRequestController::class, 'show'])->name('loans.show');
    Route::get('/loans/{loan}/edit', [LoanRequestController::class, 'edit'])->name('loans.edit');
    Route::put('/loans/{loan}', [LoanRequestController::class, 'update'])->name('loans.update');
    Route::delete('/loans/{loan}', [LoanRequestController::class, 'destroy'])->name('loans.destroy');

    // Support Tickets
    Route::get('/tickets', [SupportTicketController::class, 'index'])->name('tickets.index');
    Route::get('/tickets/create/{farm?}', [SupportTicketController::class, 'create'])->name('tickets.create');
    Route::post('/tickets', [SupportTicketController::class, 'store'])->name('tickets.store');
    Route::get('/tickets/{ticket}', [SupportTicketController::class, 'show'])->name('tickets.show');
    Route::post('/tickets/{ticket}/reply', [SupportTicketController::class, 'reply'])->name('tickets.reply');
    Route::put('/tickets/{ticket}/close', [SupportTicketController::class, 'close'])->name('tickets.close');
    Route::put('/tickets/{ticket}/reopen', [SupportTicketController::class, 'reopen'])->name('tickets.reopen');

    // No catch-all route to prevent redirect loops

    // Agent Logout Route
    Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout');
});
