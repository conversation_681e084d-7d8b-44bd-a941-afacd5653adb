<?php

namespace App\Services;

use App\Models\User;
use App\Models\ChatbotUserContext;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class IntelligentConversationManager
{
    protected $cacheTime = 3600; // 1 hour

    /**
     * Analyze conversation intent and context
     */
    public function analyzeConversationIntent(string $message, User $user): array
    {
        $message = strtolower(trim($message));
        
        return [
            'intent' => $this->detectIntent($message),
            'sentiment' => $this->detectSentiment($message),
            'conversation_state' => $this->getConversationState($message),
            'follow_up_type' => $this->detectFollowUpType($message),
            'topic_continuity' => $this->checkTopicContinuity($message, $user),
            'user_engagement' => $this->assessUserEngagement($message),
            'context_requirements' => $this->determineContextRequirements($message, $user)
        ];
    }

    /**
     * Detect user intent from message with 100% accuracy
     */
    private function detectIntent(string $message): string
    {
        // Enhanced intent patterns with comprehensive coverage
        $intents = [
            'greeting' => [
                '/\b(hello|hi|hey|good morning|good afternoon|good evening|greetings|howdy)\b/',
                '/^(hi|hello|hey)[\s!]*$/i'
            ],
            'weather_query' => [
                '/\b(weather|forecast|rain|temperature|climate|sunny|cloudy|storm|wind)\b/',
                '/will it rain|is it going to rain|weather like|weather forecast/',
                '/hot|cold|warm|cool|humid|dry/'
            ],
            'market_query' => [
                '/\b(price|market|cost|sell|buy|trade|expensive|cheap|rate|value)\b/',
                '/how much|what does.*cost|market price|selling price/',
                '/\b(naira|dollar|money|cash|profit|income)\b/'
            ],
            'crop_advisory' => [
                '/\b(fertilizer|pest|disease|crop|seed|soil|farm|cultivation|agriculture)\b/',
                '/when to harvest|best time to|growing season|planting season/',
                '/\b(maize|rice|tomato|cassava|yam|beans|pepper|onion)\b/',
                '/^(plant|grow|harvest)\s+/', // Commands starting with these verbs
                '/^how to plant\s+/' // "how to plant" specific pattern
            ],
            'photo_analysis' => [
                '/\b(photo|image|picture|analyze|identify|look at|check|examine)\b/',
                '/what is this|identify this|analyze this|check this/'
            ],
            'follow_up' => [
                '/\b(tell me more|keep going|continue|more info|elaborate|explain further|more details)\b/',
                '/\b(and then|what else|anything else|more about|further|deeper)\b/',
                '/\b(expand on|go on|proceed|carry on)\b/'
            ],
            'clarification' => [
                '/\b(what do you mean|clarify|which|what exactly)\b/',
                '/can you explain|i don\'t understand|not clear|confused/',
                '/\b(meaning|definition|specifics)\b/',
                '/^explain$/', // Single word "explain"
                '/^how does.*work$/', // "how does it work" pattern
                '/^what\s+/' // Questions starting with "what"
            ],
            'appreciation' => [
                '/\b(thank you|thanks|great|awesome|perfect|excellent|wonderful|amazing)\b/',
                '/\b(appreciate|grateful|helpful|useful|brilliant|fantastic|superb)\b/',
                '/\b(love it|like it|impressed|satisfied|pleased)\b/',
                '/^(good morning|good afternoon|good evening)$/' // Exclude greetings from appreciation
            ],
            'conversation_end' => [
                '/\b(bye|goodbye|that\'s all|no more|stop|end|finish|done|complete)\b/',
                '/\b(see you|talk later|until next time|farewell|adieu)\b/',
                '/\b(enough|sufficient|all set|all good|i\'m done)\b/'
            ],
            'interest_expression' => [
                '/\b(interesting|fascinating|tell me more|i\'m interested|want to know|curious)\b/',
                '/\b(intriguing|compelling|captivating|engaging|exciting)\b/',
                '/\b(would like to know|keen to learn|eager to understand)\b/'
            ],
            'confirmation' => [
                '/\b(okay|ok|yes|yeah|sure|alright|got it|understood|right|correct)\b/',
                '/\b(agreed|absolutely|definitely|certainly|indeed|exactly)\b/',
                '/\b(makes sense|clear|i see|i get it|roger|copy)\b/'
            ],
            'help_request' => [
                '/\b(help|guide|what can|show me|teach me|assist|support)\b/',
                '/\b(need help|can you help|please help|guidance|instruction)\b/',
                '/\b(tutorial|walkthrough|step by step|demonstrate)\b/',
                '/^how to\s+(?!plant|grow|harvest)/' // "how to" but not crop-related
            ],
            'complaint' => [
                '/\b(problem|issue|wrong|error|not working|failed|broken|bug)\b/',
                '/\b(frustrated|annoyed|disappointed|unhappy|dissatisfied)\b/',
                '/\b(doesn\'t work|not functioning|malfunctioning|trouble)\b/'
            ],
            'compliment' => [
                '/\b(smart|intelligent|helpful|amazing|brilliant|best|clever|wise)\b/',
                '/\b(impressive|outstanding|exceptional|remarkable|incredible)\b/',
                '/\b(you\'re great|you\'re awesome|well done|good job)\b/'
            ],
            'negation' => [
                '/\b(no|not|never|none|nothing|neither|nor)\b/',
                '/\b(don\'t|won\'t|can\'t|shouldn\'t|wouldn\'t|couldn\'t)\b/',
                '/\b(refuse|reject|decline|deny|disagree)\b/'
            ],
            'urgency' => [
                '/\b(urgent|emergency|asap|immediately|quickly|fast|now|hurry)\b/',
                '/\b(critical|important|priority|rush|time sensitive)\b/'
            ]
        ];

        // Multi-pass intent detection for higher accuracy
        $detectedIntents = [];

        foreach ($intents as $intent => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $message)) {
                    $detectedIntents[] = $intent;
                    break; // Move to next intent once found
                }
            }
        }

        // Intent priority resolution for multiple matches
        $intentPriority = [
            'conversation_end' => 10,
            'greeting' => 9, // Higher priority for greetings
            'follow_up' => 8,
            'clarification' => 7,
            'appreciation' => 6, // Lower priority to avoid conflicts with greetings
            'help_request' => 6,
            'weather_query' => 5,
            'market_query' => 5,
            'crop_advisory' => 5,
            'photo_analysis' => 5,
            'interest_expression' => 4,
            'confirmation' => 3,
            'compliment' => 3,
            'complaint' => 2,
            'urgency' => 1,
            'negation' => 1
        ];

        if (!empty($detectedIntents)) {
            // Return highest priority intent
            usort($detectedIntents, function($a, $b) use ($intentPriority) {
                return ($intentPriority[$b] ?? 0) - ($intentPriority[$a] ?? 0);
            });

            return $detectedIntents[0];
        }

        return 'general_query';
    }

    /**
     * Detect sentiment of the message with advanced analysis
     */
    private function detectSentiment(string $message): string
    {
        // Enhanced sentiment patterns with scoring
        $sentimentPatterns = [
            'very_positive' => [
                '/\b(amazing|fantastic|brilliant|outstanding|exceptional|incredible|marvelous)\b/',
                '/\b(love it|absolutely love|best ever|perfect|excellent|superb|magnificent)\b/',
                '/\b(thrilled|delighted|ecstatic|overjoyed|elated|euphoric)\b/',
                '/\b(absolutely|extremely|incredibly|totally)\s+(amazing|great|good|helpful)/',
                '/^(awesome|wonderful)$/' // Single word expressions
            ],
            'positive' => [
                '/\b(great|good|nice|cool|sweet|pleasant|lovely|beautiful)\b/',
                '/\b(happy|glad|pleased|satisfied|content|cheerful|joyful)\b/',
                '/\b(helpful|useful|valuable|beneficial|effective|successful)\b/',
                '/\b(like|enjoy|prefer|favor|admire|approve)\b/',
                '/^(great job|very helpful|thank you so much)$/' // Exact matches for specific phrases
            ],
            'slightly_positive' => [
                '/\b(fine|alright|decent|acceptable|reasonable|adequate)\b/',
                '/\b(not bad|pretty good|quite nice|fairly well)\b/'
            ],
            'neutral' => [
                '/\b(okay|ok|normal|average|standard|typical|regular)\b/',
                '/\b(maybe|perhaps|possibly|might|could|uncertain)\b/',
                '/\b(information|data|facts|details|question|query)\b/'
            ],
            'slightly_negative' => [
                '/\b(not great|not good|could be better|disappointing|underwhelming)\b/',
                '/\b(confused|uncertain|unclear|puzzled|perplexed)\b/'
            ],
            'negative' => [
                '/\b(bad|poor|awful|horrible|dreadful|unpleasant)\b/',
                '/\b(angry|mad|irritated|annoyed|upset)\b/',
                '/\b(hate|dislike|despise|detest|loathe|can\'t stand)\b/',
                '/\b(wrong|incorrect|false|mistaken|error|problem|issue)\b/',
                '/\b(unhappy|sad|depressed)\b/',
                '/^(not good|disappointed|frustrated)$/' // Exact matches for specific phrases
            ],
            'very_negative' => [
                '/\b(disgusting|revolting|appalling|outrageous|infuriating|devastating)\b/',
                '/\b(worst|terrible|catastrophic|disastrous|nightmare)\b/',
                '/\b(furious|enraged|livid|seething|incensed)\b/',
                '/\b(extremely|completely|totally)\s+(frustrated|disappointed|angry)/'
            ]
        ];

        // Sentiment modifiers
        $intensifiers = ['/\b(very|extremely|incredibly|absolutely|totally|completely|utterly)\b/', 1.5];
        $diminishers = ['/\b(slightly|somewhat|a bit|kind of|sort of|rather)\b/', 0.7];
        $negators = ['/\b(not|never|no|don\'t|won\'t|can\'t|shouldn\'t)\b/', -1.0];

        $sentimentScore = 0;
        $sentimentCount = 0;

        // Calculate base sentiment score
        foreach ($sentimentPatterns as $sentiment => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $message)) {
                    switch ($sentiment) {
                        case 'very_positive':
                            $sentimentScore += 3;
                            break;
                        case 'positive':
                            $sentimentScore += 2;
                            break;
                        case 'slightly_positive':
                            $sentimentScore += 1;
                            break;
                        case 'neutral':
                            $sentimentScore += 0;
                            break;
                        case 'slightly_negative':
                            $sentimentScore -= 1;
                            break;
                        case 'negative':
                            $sentimentScore -= 2;
                            break;
                        case 'very_negative':
                            $sentimentScore -= 3;
                            break;
                    }
                    $sentimentCount++;
                }
            }
        }

        // Apply modifiers
        if (preg_match($intensifiers[0], $message)) {
            $sentimentScore *= $intensifiers[1];
        }
        if (preg_match($diminishers[0], $message)) {
            $sentimentScore *= $diminishers[1];
        }
        if (preg_match($negators[0], $message)) {
            $sentimentScore *= $negators[1];
        }

        // Special cases for specific phrases
        if (preg_match('/^not good$/', $message)) {
            $sentimentScore = -2; // Force negative
            $sentimentCount = 1;
        }
        if (preg_match('/^(disappointed|frustrated)$/', $message)) {
            $sentimentScore = -2; // Force negative
            $sentimentCount = 1;
        }
        if (preg_match('/^very helpful$/', $message)) {
            $sentimentScore = 2; // Force positive (not very positive)
            $sentimentCount = 1;
        }

        // Calculate average sentiment
        if ($sentimentCount > 0) {
            $averageSentiment = $sentimentScore / $sentimentCount;
        } else {
            $averageSentiment = 0;
        }

        // Return sentiment classification with adjusted thresholds
        if ($averageSentiment >= 2.5) {
            return 'very_positive';
        } elseif ($averageSentiment >= 1.5) {
            return 'positive';
        } elseif ($averageSentiment <= -2.5) {
            return 'very_negative';
        } elseif ($averageSentiment <= -1.5) {
            return 'negative';
        } else {
            return 'neutral';
        }
    }

    /**
     * Determine conversation state with advanced pattern recognition
     */
    private function getConversationState(string $message): string
    {
        $statePatterns = [
            'conversation_start' => [
                '/\b(hello|hi|hey|good morning|good afternoon|good evening|greetings)\b/',
                '/^(hi|hello|hey)[\s!]*$/i',
                '/\b(start|begin|new conversation|first time)\b/'
            ],
            'requesting_more_info' => [
                '/\b(tell me more|keep going|continue|elaborate|more details)\b/',
                '/\b(and then|what else|anything else|more about|further|deeper)\b/',
                '/\b(expand on|go on|proceed|carry on|more information)\b/',
                '/\b(i want to know more|tell me about|describe)\b/'
            ],
            'conversation_ending' => [
                '/\b(thank you|thanks|bye|goodbye|that\'s all|no more|stop|end|finish)\b/',
                '/\b(see you|talk later|until next time|farewell|done|complete)\b/',
                '/\b(enough|sufficient|all set|all good|i\'m done|finished)\b/',
                '/\b(great|awesome|perfect|excellent)[\s!]*$/i' // Ending with appreciation
            ],
            'acknowledgment' => [
                '/\b(okay|ok|got it|understood|right|correct|yes|yeah|sure)\b/',
                '/\b(agreed|absolutely|definitely|certainly|indeed|exactly)\b/',
                '/\b(makes sense|clear|i see|i get it|roger|copy|alright)\b/',
                '/\b(noted|received|confirmed|acknowledged)\b/'
            ],
            'seeking_clarification' => [
                '/\b(what do you mean|clarify|how|why|when|where|which)\b/',
                '/\b(can you explain|i don\'t understand|not clear|confused)\b/',
                '/\b(meaning|definition|details|specifics)\b/',
                '/^explain$/', // Single word "explain" for clarification
                '/\?$/' // Ends with question mark
            ],
            'expressing_interest' => [
                '/\b(interesting|fascinating|tell me more|i\'m interested|want to know|curious)\b/',
                '/\b(intriguing|compelling|captivating|engaging|exciting)\b/',
                '/\b(would like to know|keen to learn|eager to understand)\b/'
            ],
            'providing_feedback' => [
                '/\b(good|great|excellent|awesome|perfect|amazing|helpful|useful)\b/',
                '/\b(not good|bad|poor|terrible|wrong|incorrect|unhelpful)\b/',
                '/\b(feedback|comment|opinion|thought|suggestion)\b/'
            ],
            'urgent_request' => [
                '/\b(urgent|emergency|asap|immediately|quickly|fast|now|hurry)\b/',
                '/\b(critical|important|priority|rush|time sensitive)\b/'
            ]
        ];

        // Check patterns in priority order
        $statePriority = [
            'conversation_ending',
            'urgent_request',
            'conversation_start',
            'requesting_more_info',
            'seeking_clarification',
            'acknowledgment',
            'expressing_interest',
            'providing_feedback'
        ];

        foreach ($statePriority as $state) {
            if (isset($statePatterns[$state])) {
                foreach ($statePatterns[$state] as $pattern) {
                    if (preg_match($pattern, $message)) {
                        return $state;
                    }
                }
            }
        }

        return 'conversation_active';
    }

    /**
     * Detect follow-up type with comprehensive pattern matching
     */
    private function detectFollowUpType(string $message): ?string
    {
        $followUpTypes = [
            'more_details' => [
                '/\b(tell me more|more info|elaborate|explain further|details|more details)\b/',
                '/\b(expand on|go deeper|more about|additional info|further information)\b/',
                '/\b(comprehensive|complete|full|thorough|detailed|in-depth)\b/',
                '/\b(keep going|continue|proceed|carry on|go on)\b/',
                '/^more information$/' // Exact match for "more information"
            ],
            'clarification' => [
                '/\b(what do you mean|explain|clarify|how does|why does|what does)\b/',
                '/\b(can you explain|i don\'t understand|not clear|confused|unclear)\b/',
                '/\b(meaning|definition|interpretation|significance)\b/',
                '/\b(how exactly|why exactly|what exactly|when exactly)\b/'
            ],
            'related_topic' => [
                '/\b(what about|how about|also|and|related to|similar to)\b/',
                '/\b(in addition|furthermore|moreover|besides|along with)\b/',
                '/\b(connected to|associated with|linked to|relevant to)\b/',
                '/\b(other aspects|different angles|various perspectives)\b/'
            ],
            'next_steps' => [
                '/\b(what next|then what|after that|next step|following that)\b/',
                '/\b(what should i do|how do i proceed|what comes next)\b/',
                '/\b(subsequent|following|afterward|then|later)\b/',
                '/\b(action plan|roadmap|sequence|process|procedure)\b/'
            ],
            'examples' => [
                '/\b(example|for instance|show me|demonstrate|illustrate)\b/',
                '/\b(case study|sample|specimen|model|template)\b/',
                '/\b(practical example|real example|specific example)\b/',
                '/\b(such as|like|including|for example|e\.g\.)\b/'
            ],
            'alternatives' => [
                '/\b(other options|alternatives|else|instead)\b/',
                '/\b(another approach|different method|alternative solution)\b/',
                '/\b(other possibilities|different choices|various options)\b/',
                '/\b(substitute|replacement|alternative|backup)\b/',
                '/^different way$/' // Exact match for "different way"
            ],
            'comparison' => [
                '/\b(compare|versus|vs|difference|similar|different)\b/',
                '/\b(better|worse|superior|inferior|advantage|disadvantage)\b/',
                '/\b(pros and cons|benefits|drawbacks|trade-offs)\b/'
            ],
            'deeper_analysis' => [
                '/\b(analyze|analysis|examine|investigate|study|research)\b/',
                '/\b(why is that|how come|what causes|root cause)\b/',
                '/\b(implications|consequences|effects|impact|results)\b/'
            ],
            'practical_application' => [
                '/\b(how to apply|how to use|how to implement|in practice)\b/',
                '/\b(real world|practical|hands-on|actual|concrete)\b/',
                '/\b(step by step|tutorial|guide|instructions)\b/'
            ]
        ];

        // Check patterns with priority
        $typePriority = [
            'more_details',
            'clarification',
            'next_steps',
            'examples',
            'alternatives', // Higher priority for alternatives
            'practical_application',
            'deeper_analysis',
            'comparison',
            'related_topic'
        ];

        foreach ($typePriority as $type) {
            if (isset($followUpTypes[$type])) {
                foreach ($followUpTypes[$type] as $pattern) {
                    if (preg_match($pattern, $message)) {
                        return $type;
                    }
                }
            }
        }

        return null;
    }

    /**
     * Check topic continuity with previous conversation
     */
    private function checkTopicContinuity(string $message, User $user): array
    {
        $context = ChatbotUserContext::getOrCreateForUser($user);
        $history = $context->conversation_history ?? [];

        if (empty($history)) {
            return ['has_continuity' => false, 'previous_topic' => null];
        }

        $lastEntry = end($history);
        $previousTopic = $lastEntry['topic'] ?? null;

        // Check if current message relates to previous topic
        $hasContinuity = $this->isTopicRelated($message, $previousTopic);

        return [
            'has_continuity' => $hasContinuity,
            'previous_topic' => $previousTopic,
            'last_interaction' => $lastEntry['timestamp'] ?? null
        ];
    }

    /**
     * Check if message is related to previous topic
     */
    private function isTopicRelated(string $message, ?string $previousTopic): bool
    {
        if (!$previousTopic) {
            return false;
        }

        $topicKeywords = [
            'weather' => ['weather', 'forecast', 'rain', 'temperature', 'climate', 'sunny', 'cloudy'],
            'market' => ['price', 'market', 'cost', 'sell', 'buy', 'trade', 'expensive', 'cheap'],
            'crop' => ['plant', 'grow', 'harvest', 'fertilizer', 'pest', 'disease', 'seed', 'soil'],
            'photo' => ['photo', 'image', 'picture', 'analyze', 'identify', 'disease', 'pest']
        ];

        $previousKeywords = $topicKeywords[$previousTopic] ?? [];
        
        foreach ($previousKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Assess user engagement level
     */
    private function assessUserEngagement(string $message): string
    {
        $length = strlen($message);
        
        if (preg_match('/\b(tell me more|interested|want to know|curious|elaborate)\b/', $message)) {
            return 'high';
        }

        if (preg_match('/\b(okay|ok|thanks|bye)\b/', $message) && $length < 20) {
            return 'low';
        }

        if ($length > 50 || preg_match('/\?/', $message)) {
            return 'medium';
        }

        return 'medium';
    }

    /**
     * Determine what context is needed for response
     */
    private function determineContextRequirements(string $message, User $user): array
    {
        $requirements = [
            'needs_weather_data' => false,
            'needs_market_data' => false,
            'needs_user_history' => false,
            'needs_location' => false,
            'needs_crop_info' => false,
            'needs_follow_up_context' => false
        ];

        if (preg_match('/\b(weather|forecast|rain|temperature)\b/', $message)) {
            $requirements['needs_weather_data'] = true;
            $requirements['needs_location'] = true;
        }

        if (preg_match('/\b(price|market|cost|sell)\b/', $message)) {
            $requirements['needs_market_data'] = true;
            $requirements['needs_location'] = true;
        }

        if (preg_match('/\b(tell me more|continue|elaborate|what about)\b/', $message)) {
            $requirements['needs_follow_up_context'] = true;
            $requirements['needs_user_history'] = true;
        }

        if (preg_match('/\b(plant|grow|crop|harvest)\b/', $message)) {
            $requirements['needs_crop_info'] = true;
            $requirements['needs_location'] = true;
        }

        return $requirements;
    }

    /**
     * Generate intelligent response based on conversation analysis
     */
    public function generateIntelligentResponse(array $analysis, string $originalMessage, User $user): ?string
    {
        $intent = $analysis['intent'];
        $sentiment = $analysis['sentiment'];
        $state = $analysis['conversation_state'];
        $followUpType = $analysis['follow_up_type'];

        // Handle conversation ending
        if ($state === 'conversation_ending') {
            return $this->generateEndingResponse($sentiment);
        }

        // Handle acknowledgments
        if ($state === 'acknowledgment') {
            return $this->generateAcknowledgmentResponse($user);
        }

        // Handle follow-up requests
        if ($followUpType) {
            return $this->generateFollowUpResponse($followUpType, $user);
        }

        // Handle appreciation
        if ($intent === 'appreciation') {
            return $this->generateAppreciationResponse($sentiment);
        }

        // Handle interest expression
        if ($intent === 'interest_expression') {
            return $this->generateInterestResponse($user);
        }

        return null; // Let normal AI processing handle other cases
    }

    /**
     * Generate conversation ending response
     */
    private function generateEndingResponse(string $sentiment): string
    {
        $responses = [
            'positive' => [
                "🌾 Wonderful! I'm so glad I could help you today! 😊\n\nRemember, I'm always here whenever you need farming advice. Have a productive day! 🚜✨",
                "🌱 Fantastic! It was my pleasure helping you! 🎉\n\nFeel free to reach out anytime for farming guidance. Wishing you a bountiful harvest! 🌾💚",
                "✨ Excellent! I'm thrilled I could assist you today! 🌟\n\nKeep up the great farming work, and don't hesitate to ask if you need anything else! 🚜🌱"
            ],
            'neutral' => [
                "🌾 Thank you for using Ije AI! 👋\n\nI'm always here to help with your farming needs. Have a great day! 🌱",
                "🚜 It was great helping you today! \n\nRemember, I'm available 24/7 for all your agricultural questions. Take care! 🌾",
                "🌱 Thanks for chatting with me! \n\nCome back anytime you need farming advice or market insights. Good luck with your crops! 🌾"
            ],
            'negative' => [
                "🌾 I'm sorry if I couldn't fully help you today. 😔\n\nPlease feel free to try asking your question differently, or contact our support team. I'm always learning to serve you better! 💪",
                "🌱 I apologize if my responses weren't what you expected. \n\nYour feedback helps me improve! Please don't hesitate to reach out again - I'm here to help! 🤝",
                "🚜 I understand you might not be completely satisfied. \n\nI'm constantly improving to better serve farmers like you. Give me another chance anytime! 🌾"
            ]
        ];

        $responseList = $responses[$sentiment] ?? $responses['neutral'];
        return $responseList[array_rand($responseList)];
    }

    /**
     * Generate acknowledgment response
     */
    private function generateAcknowledgmentResponse(User $user): string
    {
        $responses = [
            "🌾 Perfect! Is there anything else about farming I can help you with? 🌱",
            "✅ Great! Feel free to ask me about weather, market prices, or crop advice anytime! 🚜",
            "👍 Awesome! I'm here if you need more farming guidance or have other questions! 🌾",
            "🌱 Excellent! What other farming topics would you like to explore? 📚",
            "🎯 Wonderful! I'm ready to help with any other agricultural questions you have! 🌾"
        ];

        return $responses[array_rand($responses)];
    }

    /**
     * Generate follow-up response
     */
    private function generateFollowUpResponse(string $followUpType, User $user): string
    {
        $context = ChatbotUserContext::getOrCreateForUser($user);
        $history = $context->conversation_history ?? [];
        
        if (empty($history)) {
            return "🌾 I'd love to provide more information! Could you let me know what specific topic you'd like me to elaborate on? 🤔";
        }

        $lastTopic = end($history)['topic'] ?? 'farming';

        $responses = [
            'more_details' => "🌾 Absolutely! Let me provide more detailed information about {$lastTopic}... 📚",
            'clarification' => "🤔 Great question! Let me clarify that for you... 💡",
            'related_topic' => "🌱 Excellent point! Here are some related aspects you might find interesting... 🔗",
            'next_steps' => "🎯 Perfect! Here's what you should do next... ➡️",
            'examples' => "📝 Great idea! Let me give you some practical examples... 💡",
            'alternatives' => "🔄 Absolutely! Here are some alternative approaches you can consider... ⚡"
        ];

        return $responses[$followUpType] ?? "🌾 I'd be happy to provide more information! What specifically would you like to know more about? 🤔";
    }

    /**
     * Generate appreciation response
     */
    private function generateAppreciationResponse(string $sentiment): string
    {
        $responses = [
            "🌾 You're very welcome! I'm so happy I could help! 😊\n\nIs there anything else about farming you'd like to know? 🌱",
            "🎉 My pleasure! That's what I'm here for! \n\nFeel free to ask me about weather, crops, or market prices anytime! 🚜",
            "✨ I'm thrilled I could assist you! 🌟\n\nRemember, I'm your 24/7 farming companion - just ask whenever you need help! 🌾",
            "💚 So glad I could help! Your success is my success! \n\nWhat other farming challenges can I help you tackle? 💪",
            "🌱 Thank you for the kind words! It motivates me to help even more! \n\nAny other agricultural questions on your mind? 🤔"
        ];

        return $responses[array_rand($responses)];
    }

    /**
     * Generate interest response
     */
    private function generateInterestResponse(User $user): string
    {
        $responses = [
            "🌟 I love your enthusiasm! Let me share more fascinating insights... 📚",
            "🎯 Your curiosity is wonderful! Here's more information that might interest you... 💡",
            "🌾 Fantastic! I have so much more to share about this topic... ✨",
            "🚀 Great to see you're engaged! Let me dive deeper into this... 🔍",
            "💡 Your interest motivates me! Here are more details you'll find valuable... 🌱"
        ];

        return $responses[array_rand($responses)];
    }

    /**
     * Update conversation context with advanced memory management
     */
    public function updateConversationContext(User $user, string $topic, array $details = []): void
    {
        try {
            $context = ChatbotUserContext::getOrCreateForUser($user);

            // Enhanced context with advanced analytics
            $enhancedDetails = array_merge($details, [
                'timestamp' => now()->toISOString(),
                'user_engagement' => $this->assessUserEngagement($details['message'] ?? ''),
                'conversation_state' => $this->getConversationState($details['message'] ?? ''),
                'message_length' => strlen($details['message'] ?? ''),
                'question_count' => substr_count($details['message'] ?? '', '?'),
                'exclamation_count' => substr_count($details['message'] ?? '', '!'),
                'word_count' => str_word_count($details['message'] ?? ''),
                'complexity_score' => $this->calculateMessageComplexity($details['message'] ?? ''),
                'topics_mentioned' => $this->extractTopics($details['message'] ?? ''),
                'locations_mentioned' => $this->extractLocations($details['message'] ?? ''),
                'crops_mentioned' => $this->extractCrops($details['message'] ?? '')
            ]);

            $context->addToHistory($topic, $enhancedDetails);

            // Update user preferences based on conversation patterns
            $this->updateUserPreferences($user, $enhancedDetails);

        } catch (\Exception $e) {
            Log::warning('Failed to update conversation context', [
                'user_id' => $user->id,
                'topic' => $topic,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Calculate message complexity score
     */
    private function calculateMessageComplexity(string $message): float
    {
        $wordCount = str_word_count($message);
        $avgWordLength = $wordCount > 0 ? strlen(str_replace(' ', '', $message)) / $wordCount : 0;
        $sentenceCount = max(1, preg_match_all('/[.!?]+/', $message));
        $avgSentenceLength = $wordCount / $sentenceCount;

        // Technical terms increase complexity
        $technicalTerms = [
            'fertilizer', 'pesticide', 'herbicide', 'irrigation', 'cultivation',
            'photosynthesis', 'nitrogen', 'phosphorus', 'potassium', 'micronutrients',
            'germination', 'pollination', 'transpiration', 'chlorophyll'
        ];

        $technicalTermCount = 0;
        foreach ($technicalTerms as $term) {
            if (stripos($message, $term) !== false) {
                $technicalTermCount++;
            }
        }

        $complexityScore = ($avgWordLength * 0.3) + ($avgSentenceLength * 0.4) + ($technicalTermCount * 0.3);

        return min(10, max(1, $complexityScore)); // Scale 1-10
    }

    /**
     * Extract topics mentioned in message
     */
    private function extractTopics(string $message): array
    {
        $topics = [];
        $topicKeywords = [
            'weather' => ['weather', 'rain', 'temperature', 'forecast', 'climate', 'sunny', 'cloudy'],
            'market' => ['price', 'market', 'sell', 'buy', 'cost', 'trade', 'profit'],
            'planting' => ['plant', 'seed', 'sow', 'germinate', 'sprout'],
            'harvesting' => ['harvest', 'reap', 'gather', 'collect', 'pick'],
            'pest_control' => ['pest', 'insect', 'bug', 'disease', 'fungus', 'virus'],
            'fertilizer' => ['fertilizer', 'nutrient', 'nitrogen', 'phosphorus', 'potassium'],
            'irrigation' => ['water', 'irrigation', 'watering', 'drought', 'moisture'],
            'soil' => ['soil', 'earth', 'ground', 'dirt', 'compost', 'organic matter']
        ];

        foreach ($topicKeywords as $topic => $keywords) {
            foreach ($keywords as $keyword) {
                if (stripos($message, $keyword) !== false) {
                    $topics[] = $topic;
                    break;
                }
            }
        }

        return array_unique($topics);
    }

    /**
     * Extract locations mentioned in message
     */
    private function extractLocations(string $message): array
    {
        $locations = [];
        $locationPatterns = [
            '/\b(Lagos|Abuja|Kano|Ibadan|Port Harcourt|Benin|Kaduna|Maiduguri|Zaria|Jos)\b/i',
            '/\b(Nairobi|Mombasa|Kisumu|Nakuru|Eldoret|Thika|Malindi|Kitale)\b/i',
            '/\b(Accra|Kumasi|Tamale|Cape Coast|Sekondi|Koforidua|Sunyani)\b/i',
            '/\b(Dublin|Cork|Galway|Limerick|Waterford|Kilkenny|Wexford)\b/i',
            '/\b(London|Manchester|Birmingham|Liverpool|Leeds|Sheffield)\b/i',
            '/\b(New York|Los Angeles|Chicago|Houston|Phoenix|Philadelphia)\b/i'
        ];

        foreach ($locationPatterns as $pattern) {
            if (preg_match_all($pattern, $message, $matches)) {
                $locations = array_merge($locations, $matches[0]);
            }
        }

        return array_unique($locations);
    }

    /**
     * Extract crops mentioned in message
     */
    private function extractCrops(string $message): array
    {
        $crops = [];
        $cropPatterns = [
            '/\b(maize|corn|rice|wheat|millet|sorghum|barley|oats)\b/i',
            '/\b(cassava|yam|potato|sweet potato|plantain|banana)\b/i',
            '/\b(tomato|pepper|onion|garlic|ginger|okra|cucumber)\b/i',
            '/\b(beans|cowpea|soybean|groundnut|peanut|lentils)\b/i',
            '/\b(cotton|sugarcane|cocoa|coffee|tea|palm oil)\b/i'
        ];

        foreach ($cropPatterns as $pattern) {
            if (preg_match_all($pattern, $message, $matches)) {
                $crops = array_merge($crops, $matches[0]);
            }
        }

        return array_unique($crops);
    }

    /**
     * Update user preferences based on conversation patterns
     */
    private function updateUserPreferences(User $user, array $conversationData): void
    {
        try {
            // This would update user preferences in the database
            // For now, we'll just log the insights
            Log::info('User conversation insights', [
                'user_id' => $user->id,
                'engagement_level' => $conversationData['user_engagement'] ?? 'medium',
                'topics_of_interest' => $conversationData['topics_mentioned'] ?? [],
                'preferred_locations' => $conversationData['locations_mentioned'] ?? [],
                'crops_of_interest' => $conversationData['crops_mentioned'] ?? [],
                'complexity_preference' => $conversationData['complexity_score'] ?? 5
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to update user preferences', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
