<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/dashboard';

    /**
     * The path to the insurance company dashboard.
     *
     * Insurance companies are redirected here after authentication.
     *
     * @var string
     */
    public const INSURANCE_HOME = '/insurance/dashboard';

    /**
     * The path to the admin dashboard.
     *
     * Admins are redirected here after authentication.
     *
     * @var string
     */
    public const ADMIN_HOME = '/admin/dashboard';

    /**
     * The path to the agent dashboard.
     *
     * Agents are redirected here after authentication.
     *
     * @var string
     */
    public const AGENT_HOME = '/agent/dashboard';

    /**
     * The path to the bank dashboard.
     *
     * Bank users are redirected here after authentication.
     *
     * @var string
     */
    public const BANK_HOME = '/bank';

    /**
     * The path to the manager dashboard.
     *
     * Managers are redirected here after authentication.
     *
     * @var string
     */
    public const MANAGER_HOME = '/manager/dashboard';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/auth.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            // Developer Portal Routes
            Route::middleware('web')
                ->prefix('developers')
                ->group(base_path('routes/developers.php'));

            // Developer API Routes
            Route::middleware('api')
                ->prefix('api/dev')
                ->group(base_path('routes/developer-api.php'));

            // Admin Developer Portal Routes
            Route::group([], base_path('routes/admin-developers.php'));

            // Agent Routes
            Route::middleware('web')
                ->namespace('App\Http\Controllers')
                ->group(base_path('routes/agent.php'));

            // Manager Routes
            Route::middleware('web')
                ->namespace('App\Http\Controllers')
                ->group(base_path('routes/manager.php'));

            // Admin Routes
            Route::middleware('web')
                ->namespace('App\Http\Controllers')
                ->group(base_path('routes/admin.php'));

            // Parametric Insurance Routes
            Route::middleware('web')
                ->prefix('policies')
                ->group(base_path('routes/parametric.php'));

            // Admin Event Routes are loaded in bootstrap/app.php

            // Chatbot Routes
            Route::middleware('web')
                ->group(base_path('routes/chatbot.php'));
        });
    }
}