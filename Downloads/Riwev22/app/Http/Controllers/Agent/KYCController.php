<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\KYC;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Rules\MinimumAge;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class KYCController extends Controller
{
    /**
     * Show the KYC details for a specific user (read-only view).
     */
    public function show(User $user): View
    {
        $agent = Auth::user();

        // Check authorization: agent can access if they created the user, or if they're admin/super-admin
        if ($user->created_by !== $agent->id && !$agent->hasAnyRole(['admin', 'super-admin'])) {
            abort(403, 'Unauthorized action. You can only access KYC for users you have created.');
        }

        // Get existing KYC data - redirect to create if no KYC exists
        $kyc = $user->kyc;
        if (!$kyc) {
            return redirect()->route('agent.kyc.create', $user)
                ->with('info', 'No KYC submission found for this user. Please submit KYC information.');
        }

        return view('agent.kyc.show', compact('user', 'kyc'));
    }

    /**
     * Show the KYC form for a specific user.
     */
    public function create(User $user): View
    {
        $agent = Auth::user();

        // Check authorization: agent can access if they created the user, or if they're admin/super-admin
        if ($user->created_by !== $agent->id && !$agent->hasAnyRole(['admin', 'super-admin'])) {
            abort(403, 'Unauthorized action. You can only access KYC for users you have created.');
        }

        // Get existing KYC data if available
        $kyc = $user->kyc;

        return view('agent.kyc.create', compact('user', 'kyc'));
    }

    /**
     * Store or update KYC information for a user.
     */
    public function store(Request $request, User $user): RedirectResponse
    {
        $agent = Auth::user();

        // Check authorization: agent can access if they created the user, or if they're admin/super-admin
        if ($user->created_by !== $agent->id && !$agent->hasAnyRole(['admin', 'super-admin'])) {
            abort(403, 'Unauthorized action. You can only access KYC for users you have created.');
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'id_type' => 'required|string|max:255',
            'id_number' => 'required|string|max:255',
            'id_front_image' => 'nullable|image|max:2048',
            'id_back_image' => 'nullable|image|max:2048',
            'selfie_image' => 'nullable|image|max:2048',
            'address_proof_image' => 'nullable|image|max:2048',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'postal_code' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'lga' => 'nullable|string|max:255',
            'community' => 'nullable|string|max:255',
            'date_of_birth' => ['required', 'date', 'before:today', new MinimumAge(18)],
            'gender' => 'required|string|in:male,female,other',
            'occupation' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->route('agent.kyc.create', $user)
                ->withErrors($validator)
                ->withInput();
        }

        // Get or create KYC record
        $kyc = $user->kyc ?? new KYC(['user_id' => $user->id, 'status' => 'pending']);

        // Update KYC information
        $kyc->id_type = $request->id_type;
        $kyc->id_number = $request->id_number;
        $kyc->address = $request->address;
        $kyc->city = $request->city;
        $kyc->state = $request->state;
        $kyc->lga = $request->lga;
        $kyc->community = $request->community;
        $kyc->postal_code = $request->postal_code;
        $kyc->country = $request->country;
        $kyc->date_of_birth = $request->date_of_birth;
        $kyc->gender = $request->gender;
        $kyc->occupation = $request->occupation;
        $kyc->submitted_by_agent = $agent->id;

        // If KYC was previously rejected, reset status to pending
        if ($kyc->status === 'rejected') {
            $kyc->status = 'pending';
            $kyc->rejection_reason = null;
        }

        // Handle file uploads
        $this->handleFileUpload($request, $kyc, 'id_front_image');
        $this->handleFileUpload($request, $kyc, 'id_back_image');
        $this->handleFileUpload($request, $kyc, 'selfie_image');
        $this->handleFileUpload($request, $kyc, 'address_proof_image');

        $kyc->save();

        return redirect()->route('agent.users.show', $user)
            ->with('success', 'KYC information submitted successfully for ' . $user->name . '. Verification is pending review.');
    }

    /**
     * Handle file upload for KYC documents.
     */
    private function handleFileUpload(Request $request, KYC $kyc, string $field): void
    {
        if ($request->hasFile($field) && $request->file($field)->isValid()) {
            // Delete old file if exists
            if ($kyc->$field) {
                Storage::delete('public/kyc/' . $kyc->$field);
            }

            // Store new file
            $filename = $kyc->user_id . '_' . $field . '_' . time() . '.' . $request->file($field)->extension();
            $request->file($field)->storeAs('public/kyc', $filename);
            $kyc->$field = $filename;
        }
    }
}
