<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class EventController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(): View
    {
        $events = Event::latest()->paginate(10);
        return view('admin.events.index', compact('events'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(): View
    {
        return view('admin.events.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:events,slug|regex:/^[a-z0-9-]+$/',
            'description' => 'required|string|min:10',
            'event_date' => 'required|date|after:today',
            'location' => 'required|string|max:255',
            'location_type' => 'required|in:physical,virtual',
            'virtual_meeting_url' => [
                'nullable',
                'url',
                'max:255',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->input('location_type') === 'virtual' && empty($value)) {
                        $fail('The virtual meeting URL is required when location type is virtual.');
                    }
                },
            ],
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048|dimensions:min_width=300,min_height=200',
            'speaker' => 'nullable|string|max:255',
            'registration_deadline' => 'nullable|date|before:event_date',
            'is_active' => 'boolean',
            'auto_approve_registrations' => 'boolean',
            'hide_location_until_registered' => 'boolean',
            'reminder_settings' => 'nullable|array',
            'reminder_settings.*' => 'boolean',
        ], [
            'title.required' => 'The event title is required.',
            'title.max' => 'The event title cannot exceed 255 characters.',
            'slug.regex' => 'The slug may only contain lowercase letters, numbers, and hyphens.',
            'description.required' => 'The event description is required.',
            'description.min' => 'The event description must be at least 10 characters.',
            'event_date.required' => 'The event date and time are required.',
            'event_date.after' => 'The event date must be in the future.',
            'location.required' => 'The event location is required.',
            'virtual_meeting_url.url' => 'The virtual meeting URL must be a valid URL (include http:// or https://).',
            'image.dimensions' => 'The image must be at least 300x200 pixels.',
            'registration_deadline.before' => 'The registration deadline must be before the event date.',
        ]);

        // Handle image upload
        try {
            // Check if we have a regular file upload
            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('events', 'public');
                $validated['image'] = $imagePath;
            }
            // Check if we have a chunked upload path
            elseif ($request->has('image_paths')) {
                $validated['image'] = $request->input('image_paths');
            }
        } catch (\Exception $e) {
            \Log::error('Failed to upload event image: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withErrors(['image' => 'Failed to upload image. Please try again or contact support.']);
        }

        // Set created_by to current user
        $validated['created_by'] = Auth::id();

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);

            // Ensure slug is unique
            $count = 2;
            $originalSlug = $validated['slug'];
            while (Event::where('slug', $validated['slug'])->exists()) {
                $validated['slug'] = $originalSlug . '-' . $count++;
            }
        }

        // Always set start_date and end_date from event_date
        $validated['start_date'] = $validated['event_date'];
        $validated['end_date'] = date('Y-m-d H:i:s', strtotime($validated['event_date'] . ' +2 hours'));

        // Create the event
        $event = Event::create($validated);

        return redirect()->route('admin.events.index')
            ->with('success', 'Event created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Event  $event
     * @return \Illuminate\View\View
     */
    public function show(Event $event): View
    {
        return view('admin.events.show', compact('event'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Event  $event
     * @return \Illuminate\View\View
     */
    public function edit(Event $event): View
    {
        return view('admin.events.edit', compact('event'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Event  $event
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Event $event): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:events,slug,' . $event->id . '|regex:/^[a-z0-9-]+$/',
            'description' => 'required|string|min:10',
            'event_date' => [
                'required',
                'date',
                function ($attribute, $value, $fail) use ($event) {
                    // Only validate future date for new events or if the date has changed
                    if ($event->event_date->format('Y-m-d H:i:s') !== date('Y-m-d H:i:s', strtotime($value)) &&
                        strtotime($value) < strtotime('today')) {
                        $fail('The event date must be in the future.');
                    }
                },
            ],
            'location' => 'required|string|max:255',
            'location_type' => 'required|in:physical,virtual',
            'virtual_meeting_url' => [
                'nullable',
                'url',
                'max:255',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->input('location_type') === 'virtual' && empty($value)) {
                        $fail('The virtual meeting URL is required when location type is virtual.');
                    }
                },
            ],
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048|dimensions:min_width=300,min_height=200',
            'speaker' => 'nullable|string|max:255',
            'registration_deadline' => 'nullable|date|before:event_date',
            'is_active' => 'boolean',
            'auto_approve_registrations' => 'boolean',
            'hide_location_until_registered' => 'boolean',
            'reminder_settings' => 'nullable|array',
            'reminder_settings.*' => 'boolean',
            'banner_ads' => 'nullable|array',
            'partner_logos' => 'nullable|array',
        ], [
            'title.required' => 'The event title is required.',
            'title.max' => 'The event title cannot exceed 255 characters.',
            'slug.regex' => 'The slug may only contain lowercase letters, numbers, and hyphens.',
            'description.required' => 'The event description is required.',
            'description.min' => 'The event description must be at least 10 characters.',
            'event_date.required' => 'The event date and time are required.',
            'location.required' => 'The event location is required.',
            'virtual_meeting_url.url' => 'The virtual meeting URL must be a valid URL (include http:// or https://).',
            'image.dimensions' => 'The image must be at least 300x200 pixels.',
            'registration_deadline.before' => 'The registration deadline must be before the event date.',
        ]);

        // Handle image upload
        try {
            // Check if we have a regular file upload
            if ($request->hasFile('image')) {
                // Delete old image if it exists
                if ($event->image) {
                    Storage::disk('public')->delete($event->image);
                }

                $imagePath = $request->file('image')->store('events', 'public');
                $validated['image'] = $imagePath;
            }
            // Check if we have a chunked upload path
            elseif ($request->has('image_paths')) {
                // Delete old image if it exists
                if ($event->image) {
                    Storage::disk('public')->delete($event->image);
                }

                $validated['image'] = $request->input('image_paths');
            }
        } catch (\Exception $e) {
            \Log::error('Failed to upload event image: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withErrors(['image' => 'Failed to upload image. Please try again or contact support.']);
        }

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);

            // Ensure slug is unique
            $count = 2;
            $originalSlug = $validated['slug'];
            while (Event::where('slug', $validated['slug'])->where('id', '!=', $event->id)->exists()) {
                $validated['slug'] = $originalSlug . '-' . $count++;
            }
        }

        // Always set start_date and end_date from event_date
        $validated['start_date'] = $validated['event_date'];
        $validated['end_date'] = date('Y-m-d H:i:s', strtotime($validated['event_date'] . ' +2 hours'));

        // Handle boolean fields properly (checkboxes that aren't checked don't send values)
        $validated['is_active'] = $request->has('is_active') ? true : false;
        $validated['auto_approve_registrations'] = $request->has('auto_approve_registrations') ? true : false;
        $validated['hide_location_until_registered'] = $request->has('hide_location_until_registered') ? true : false;

        // Update the event
        $event->update($validated);

        return redirect()->route('admin.events.index')
            ->with('success', 'Event updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Event  $event
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, Event $event): RedirectResponse
    {
        $forceDelete = $request->input('force_delete', false);

        if ($forceDelete) {
            // Permanently delete the event and all related data

            // Delete image if it exists
            if ($event->image) {
                Storage::disk('public')->delete($event->image);
            }

            // Delete related data first
            $event->registrations()->delete();
            $event->broadcasts()->delete();
            $event->speakers()->delete();
            $event->formFields()->delete();

            // Force delete the event (permanently remove from database)
            $event->forceDelete();

            return redirect()->route('admin.events.index')
                ->with('success', 'Event permanently deleted from database.');
        } else {
            // Soft delete the event (mark as deleted but keep in database)
            $event->delete();

            return redirect()->route('admin.events.index')
                ->with('success', 'Event moved to trash. It can be restored later.');
        }
    }


}
