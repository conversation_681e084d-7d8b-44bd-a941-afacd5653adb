<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class EventPartnerController extends Controller
{
    /**
     * Show the form for managing event partners.
     *
     * @param  \App\Models\Event  $event
     * @return \Illuminate\View\View
     */
    public function index(Event $event): View
    {
        return view('admin.events.partners.index', compact('event'));
    }

    /**
     * Store a newly created partner logo in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Event  $event
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request, Event $event): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'url' => 'nullable|url|max:255',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('events/partners', 'public');
            $validated['logo'] = $logoPath;
        }

        // Get current partner logos
        $partnerLogos = $event->partner_logos ?: [];

        // Add new partner logo
        $partnerLogos[] = [
            'name' => $validated['name'],
            'logo' => $validated['logo'],
            'url' => $validated['url'] ?? null,
            'created_at' => now()->toDateTimeString(),
        ];

        // Update the event
        $event->update(['partner_logos' => $partnerLogos]);

        return redirect()->route('admin.events.partners.index', $event)
            ->with('success', 'Partner logo added successfully.');
    }

    /**
     * Remove the specified partner logo from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Event  $event
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, Event $event): RedirectResponse
    {
        $validated = $request->validate([
            'index' => 'required|integer|min:0',
        ]);

        $index = $validated['index'];

        // Get current partner logos
        $partnerLogos = $event->partner_logos ?: [];

        // Check if the index exists
        if (isset($partnerLogos[$index])) {
            // Get the logo path
            $logoPath = $partnerLogos[$index]['logo'] ?? null;

            // Delete the logo from storage
            if ($logoPath) {
                Storage::disk('public')->delete($logoPath);
            }

            // Remove the partner logo
            array_splice($partnerLogos, $index, 1);

            // Update the event
            $event->update(['partner_logos' => $partnerLogos]);

            return redirect()->route('admin.events.partners.index', $event)
                ->with('success', 'Partner logo removed successfully.');
        }

        return redirect()->route('admin.events.partners.index', $event)
            ->with('error', 'Partner logo not found.');
    }

    /**
     * Update the order of partner logos.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Event  $event
     * @return \Illuminate\Http\RedirectResponse
     */
    public function order(Request $request, Event $event): RedirectResponse
    {
        $validated = $request->validate([
            'order' => 'required|array',
            'order.*' => 'integer|min:0',
        ]);

        $order = $validated['order'];
        $partnerLogos = $event->partner_logos ?: [];

        // Create a new array with the updated order
        $newPartnerLogos = [];
        foreach ($order as $index) {
            if (isset($partnerLogos[$index])) {
                $newPartnerLogos[] = $partnerLogos[$index];
            }
        }

        // Update the event
        $event->update(['partner_logos' => $newPartnerLogos]);

        return redirect()->route('admin.events.partners.index', $event)
            ->with('success', 'Partner logo order updated successfully.');
    }
}
