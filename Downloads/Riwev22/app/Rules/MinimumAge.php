<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Carbon\Carbon;

class MinimumAge implements ValidationRule
{
    protected int $minimumAge;

    /**
     * Create a new rule instance.
     *
     * @param int $minimumAge The minimum age required (default: 18)
     */
    public function __construct(int $minimumAge = 18)
    {
        $this->minimumAge = $minimumAge;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($value)) {
            return; // Let other validation rules handle required validation
        }

        try {
            $birthDate = Carbon::parse($value);
            $today = Carbon::today();

            // Additional check: ensure the date is not in the future
            if ($birthDate->isFuture()) {
                $fail('Date of birth cannot be in the future.');
                return;
            }

            // Calculate age accurately (same logic as JavaScript version)
            $age = $today->year - $birthDate->year;

            // Check if birthday hasn't occurred this year yet
            if ($today->month < $birthDate->month ||
                ($today->month == $birthDate->month && $today->day < $birthDate->day)) {
                $age--;
            }

            // Check if the person is old enough
            if ($age < $this->minimumAge) {
                $fail("You must be at least {$this->minimumAge} years old.");
                return;
            }

            // Additional check: ensure the date is reasonable (not more than 120 years ago)
            if ($age > 120) {
                $fail('Please enter a valid date of birth. Age cannot exceed 120 years.');
                return;
            }

        } catch (\Exception $e) {
            $fail('Please enter a valid date of birth.');
        }
    }
}
